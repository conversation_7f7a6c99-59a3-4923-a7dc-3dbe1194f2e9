import React, { useState, useRef, useCallback } from "react";

export const KNXAddressInput = ({
  value = "",
  onChange,
  onComplete,
  disabled = false,
  error = false,
  placeholder = "0.0.0",
}) => {
  const [values, setValues] = useState(() => {
    const parts = value.split(".");
    return {
      area: parts[0] || "",
      line: parts[1] || "",
      device: parts[2] || "",
    };
  });

  const refs = {
    area: useRef(null),
    line: useRef(null),
    device: useRef(null),
  };

  const validateInput = (type, value) => {
    const num = parseInt(value);
    if (isNaN(num)) return false;

    switch (type) {
      case "area": // Main Group - 5 bits (0-31)
        return num >= 0 && num <= 31;
      case "line": // Middle Group - 3 bits (0-7)
        return num >= 0 && num <= 7;
      case "device": // Sub Group - 8 bits (0-255)
        return num >= 0 && num <= 255;
      default:
        return false;
    }
  };

  const updateValue = useCallback(
    (field, newValue) => {
      const updatedValues = { ...values, [field]: newValue };
      setValues(updatedValues);

      const fullAddress = `${updatedValues.area}.${updatedValues.line}.${updatedValues.device}`;
      onChange?.(fullAddress);

      // Check if address is complete and valid
      if (updatedValues.area && updatedValues.line && updatedValues.device) {
        const isValid =
          validateInput("area", updatedValues.area) &&
          validateInput("line", updatedValues.line) &&
          validateInput("device", updatedValues.device);
        if (isValid) {
          onComplete?.(fullAddress);
        }
      }
    },
    [values, onChange, onComplete]
  );

  const handleInputChange = (field, event) => {
    const value = event.target.value;

    // Only allow numbers
    if (!/^\d*$/.test(value)) return;

    // Validate range based on field type
    if (value && !validateInput(field, value)) return;

    updateValue(field, value);

    // Auto-focus next input when reaching max length for each field
    const maxLengths = { area: 2, line: 1, device: 3 };
    if (value.length === maxLengths[field] && validateInput(field, value)) {
      const nextField =
        field === "area" ? "line" : field === "line" ? "device" : null;
      if (nextField && refs[nextField].current) {
        setTimeout(() => refs[nextField].current.focus(), 0);
      }
    }
  };

  const handleKeyDown = (field, event) => {
    // Handle "." and "/" to move to next field
    if (event.key === "." || event.key === "/") {
      event.preventDefault();
      const nextField =
        field === "area" ? "line" : field === "line" ? "device" : null;
      if (nextField && refs[nextField].current) {
        refs[nextField].current.focus();
      }
      return;
    }

    if (event.key === "Backspace" && !values[field]) {
      const prevField =
        field === "device" ? "line" : field === "line" ? "area" : null;
      if (prevField && refs[prevField].current) {
        refs[prevField].current.focus();
      }
    }

    if (event.key === "ArrowLeft") {
      const prevField =
        field === "device" ? "line" : field === "line" ? "area" : null;
      if (prevField && refs[prevField].current) {
        refs[prevField].current.focus();
      }
    }

    if (event.key === "ArrowRight") {
      const nextField =
        field === "area" ? "line" : field === "line" ? "device" : null;
      if (nextField && refs[nextField].current) {
        refs[nextField].current.focus();
      }
    }
  };

  const handlePaste = (event) => {
    event.preventDefault();
    const pastedText = event.clipboardData.getData("text");
    const parts = pastedText.split(".");

    if (parts.length === 3) {
      const [area, line, device] = parts;
      if (
        validateInput("area", area) &&
        validateInput("line", line) &&
        validateInput("device", device)
      ) {
        setValues({ area, line, device });
        const fullAddress = `${area}.${line}.${device}`;
        onChange?.(fullAddress);
        onComplete?.(fullAddress);
      }
    }
  };

  const getFieldError = (field) => {
    if (!values[field]) return false;
    return !validateInput(field, values[field]);
  };

  return (
    <div className="flex flex-col space-y-2">
      <div className="flex items-center space-x-2">
        <div className="flex items-center space-x-1">
          <input
            ref={refs.area}
            type="text"
            value={values.area}
            onChange={(e) => handleInputChange("area", e)}
            onKeyDown={(e) => handleKeyDown("area", e)}
            onPaste={handlePaste}
            disabled={disabled}
            placeholder="0"
            className={`w-12 h-10 text-center text-sm border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              error || getFieldError("area")
                ? "border-red-500 bg-red-50"
                : "border-gray-300 bg-white"
            } ${disabled ? "bg-gray-100 cursor-not-allowed" : ""}`}
            maxLength="2"
          />
          <span className="text-gray-500">/</span>
          <input
            ref={refs.line}
            type="text"
            value={values.line}
            onChange={(e) => handleInputChange("line", e)}
            onKeyDown={(e) => handleKeyDown("line", e)}
            onPaste={handlePaste}
            disabled={disabled}
            placeholder="0"
            className={`w-8 h-10 text-center text-sm border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              error || getFieldError("line")
                ? "border-red-500 bg-red-50"
                : "border-gray-300 bg-white"
            } ${disabled ? "bg-gray-100 cursor-not-allowed" : ""}`}
            maxLength="1"
          />
          <span className="text-gray-500">/</span>
          <input
            ref={refs.device}
            type="text"
            value={values.device}
            onChange={(e) => handleInputChange("device", e)}
            onKeyDown={(e) => handleKeyDown("device", e)}
            onPaste={handlePaste}
            disabled={disabled}
            placeholder="0"
            className={`w-16 h-10 text-center text-sm border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              error || getFieldError("device")
                ? "border-red-500 bg-red-50"
                : "border-gray-300 bg-white"
            } ${disabled ? "bg-gray-100 cursor-not-allowed" : ""}`}
            maxLength="3"
          />
        </div>
      </div>
    </div>
  );
};
